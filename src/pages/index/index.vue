<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page -->
<route lang="jsonc" type="home">
{
  "layout": "tabbar",
  "style": {
    // 'custom' 表示使用自定义导航栏，可以自定义标题大小
    "navigationStyle": "default",
    "navigationBarTitleText": "首页",
    "navigationBarBackgroundColor": "#ffffff",
    "navigationBarTextStyle": "black"
  }
}
</route>

<script lang="ts" setup>
import PhotoSizeList from '@/components/PhotoSizeList.vue'

defineOptions({
  name: 'Home',
})

// swiper高度
const swiperHeight = ref(0)

// tab列表数据
const tabList = ref(['常用尺寸', '各类签证', '各类证件'])

// 当前选中的tab
const currentTab = ref(0)

// z-paging实例引用
const pagingRef = ref()

// swiper列表实例引用
const swiperListRef = ref([])

// 点击搜索框
function handleSearchClick() {
  console.log('点击了搜索框')
  uni.navigateTo({
    url: '/pages/search/index',
  })
}

// 证件照类型导航
function indexGoNextPage(type: number) {
  console.log('选择证件照类型:', type)
  // 这里可以添加导航逻辑，根据type参数跳转到不同页面
  // type 0: 一寸照片
  // type 1: 二寸照片
}

// 导航到换背景页面
function navigateToCutout() {
  uni.navigateTo({
    url: '/pages/cutout/index',
  })
}

// tabs切换事件
function tabsChange(index: number) {
  console.log('tabsChange:', index)
  setCurrent(index)
}

// 下拉刷新时，通知当前显示的列表进行reload操作
function onRefresh() {
  swiperListRef.value[currentTab.value]?.reload(() => {
    // 当当前显示的列表刷新结束，结束当前页面的刷新状态
    pagingRef.value?.endRefresh()
  })
}

// 当滚动到底部时，通知当前显示的列表加载更多
function scrolltolower() {
  swiperListRef.value[currentTab.value]?.doLoadMore()
}

// swiper滑动结束
function swiperAnimationfinish(e: any) {
  setCurrent(e.detail.current)
}

// 设置swiper的高度
function heightChanged(height: number) {
  swiperHeight.value = height
}

// 设置当前tab
function setCurrent(current: number) {
  if (current !== currentTab.value) {
    // 切换tab时，将上一个tab的数据清空
    swiperListRef.value[currentTab.value]?.clear()
  }
  currentTab.value = current
  console.log('current:', current)

  console.log('swiperListRef:', swiperListRef.value[current])
  // 主动触发当前tab的数据加载
  swiperListRef.value[current]?.loadData()
}

// 测试 uni API 自动引入
onLoad(() => {
  console.log('证件照首页加载完成')
})

// 页面挂载后，加载第一个tab的数据
onMounted(() => {
  // 直接加载默认选中的第一个tab数据
  console.log('swiperListRef:', swiperListRef)
  // swiperListRef.value[currentTab.value]?.loadData()
})
</script>

<template>
  <view class="home-container">
    <!-- z-paging容器 -->
    <z-paging
      ref="pagingRef"
      refresher-only
      @on-refresh="onRefresh"
      @scrolltolower="scrolltolower"
    >
      <!-- 搜索框和图片布局 -->
      <view class="banner-view">
        <!-- 搜索框 -->
        <!-- 搜索框容器 -->
        <view class="search-container">
          <wd-search
            placeholder="搜索证件照名称、尺寸"

            hide-cancel disabled placeholder-left
            custom-style="margin: 0rpx 20rpx;"
          />
          <!-- 透明遮罩层，用于处理点击事件 -->
          <view
            class="search-overlay"
            @click="handleSearchClick"
          />
        </view>

        <!-- 图片布局 -->
        <view class="m-[15rpx_0_30rpx_0] h-[370rpx] w-full flex">
          <view class="ml-[35rpx]" @click="indexGoNextPage(0)">
            <image src="/static/icon/one-inch.png" class="h-[370rpx] w-[328rpx]" />
          </view>
          <view class="ml-[26rpx] w-[50%] flex flex-col">
            <view class="h-[172rpx] w-[332rpx]" @click="indexGoNextPage(1)">
              <image src="/static/icon/two-inch.png" class="h-[172rpx] w-[332rpx]" />
            </view>
            <view class="mt-[29rpx] h-[172rpx] w-[332rpx]" @click="navigateToCutout">
              <image src="/static/icon/change-bg.png" class="h-[172rpx] w-[332rpx]" />
            </view>
          </view>
        </view>
      </view>

      <!-- 吸顶选项卡和内容区域 -->
      <view>
        <!-- 吸顶选项卡 -->
        <view style="z-index: 100; position: sticky; top: 0;">
          <wd-tabs
            v-model="currentTab"
            color="#4E47FD"
            title-active-color="#4E47FD"
            @change="tabsChange"
          >
            <wd-tab
              v-for="(item, index) in tabList"
              :key="index"
              :title="item"
              title-style="font-size: 28rpx; font-weight: 700;"
            >
              <!-- tab内容由下方的swiper管理，这里为空 -->
            </wd-tab>
          </wd-tabs>
        </view>

        <!-- swiper内容区域 -->
        <swiper
          class="swiper"
          :style="{ height: `${swiperHeight}px` }"
          :current="currentTab"
          @animationfinish="swiperAnimationfinish"
        >
          <swiper-item
            v-for="(_, index) in tabList"
            :key="index"
            class="swiper-item"
          >
            <!-- 每个tab对应的列表组件 -->
            <PhotoSizeList
              :ref="(el) => { if (el) swiperListRef[index] = el }"
              :tab-index="index"
              :current-index="currentTab"
              @height-changed="heightChanged"
            />
          </swiper-item>
        </swiper>
      </view>
    </z-paging>
  </view>
</template>

<style lang="scss" scoped>
.home-container {
  height: 100vh;
  background-color: #f5f5f5;
}

.banner-view {
  background-color: #fff;
  padding-bottom: 20rpx;
}

.swiper {
  height: 1000px;
}

.swiper-item {
  height: 100%;
}

/* 自定义wd-tabs样式 */
:deep(.wd-tabs) {
  background-color: #fff;
  border-bottom: 1px solid #eee;
}

:deep(.wd-tab) {
  font-size: 30rpx;
  font-weight: 500;
}

:deep(.wd-tab--active) {
  color: #4e47fd;
  font-weight: 600;
}

/* 自定义tab底部线条颜色 */
:deep(.wd-tabs__line) {
  background-color: #4e47fd !important;
}

/* 如果上面的样式不生效，可以尝试这个 */
:deep(.wd-tabs .wd-tabs__line) {
  background-color: #4e47fd !important;
}

/* 搜索框容器样式 */
.search-container {
  position: relative;
}

/* 透明遮罩层样式 */
.search-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
  background-color: transparent;
}
</style>
